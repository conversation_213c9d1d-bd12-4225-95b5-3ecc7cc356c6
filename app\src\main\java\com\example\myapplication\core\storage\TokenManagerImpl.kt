package com.example.myapplication.core.storage

import javax.inject.Inject

/**
 * Implementação do gerenciador de tokens
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
class TokenManagerImpl @Inject constructor(
    private val preferencesManager: IPreferencesManager
) : ITokenManager {
    
    companion object {
        private const val KEY_ACCESS_TOKEN = "access_token"
        private const val KEY_REFRESH_TOKEN = "refresh_token"
    }
    
    override suspend fun saveAccessToken(token: String) {
        preferencesManager.saveString(KEY_ACCESS_TOKEN, token)
    }
    
    override suspend fun getAccessToken(): String? {
        return preferencesManager.getString(KEY_ACCESS_TOKEN)
    }
    
    override suspend fun saveRefreshToken(token: String) {
        preferencesManager.saveString(KEY_REFRESH_TOKEN, token)
    }
    
    override suspend fun getRefreshToken(): String? {
        return preferencesManager.getString(KEY_REFRESH_TOKEN)
    }
    
    override suspend fun clearTokens() {
        preferencesManager.remove(KEY_ACCESS_TOKEN)
        preferencesManager.remove(KEY_REFRESH_TOKEN)
    }
    
    override suspend fun isTokenValid(): Boolean {
        val accessToken = getAccessToken()
        return !accessToken.isNullOrBlank()
        // TODO: Implementar validação de expiração do token
    }
}
