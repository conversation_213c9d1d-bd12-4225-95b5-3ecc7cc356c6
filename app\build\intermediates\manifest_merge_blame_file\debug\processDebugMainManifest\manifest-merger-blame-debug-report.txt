1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permission required for network access -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:9:5-32:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:14:9-41
30        android:networkSecurityConfig="@xml/network_security_config"
30-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:19:9-69
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:15:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:16:9-35
33        android:theme="@style/Theme.MyApplication"
33-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:17:9-51
34        android:usesCleartextTraffic="true" >
34-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:18:9-44
35        <activity
35-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:21:9-31:20
36            android:name="com.example.myapplication.MainActivity"
36-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:22:13-41
37            android:exported="true"
37-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:23:13-36
38            android:label="@string/app_name"
38-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:24:13-45
39            android:theme="@style/Theme.MyApplication" >
39-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:25:13-55
40            <intent-filter>
40-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:26:13-30:29
41                <action android:name="android.intent.action.MAIN" />
41-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:27:17-69
41-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:27:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:29:17-77
43-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:29:27-74
44            </intent-filter>
45        </activity>
46        <activity
46-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bbd1320823815804c41acf77e8c3cba\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
47            android:name="androidx.compose.ui.tooling.PreviewActivity"
47-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bbd1320823815804c41acf77e8c3cba\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
48            android:exported="true" />
48-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bbd1320823815804c41acf77e8c3cba\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
49        <activity
49-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d955b063841d8ced20f95707b5602e\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
50            android:name="androidx.activity.ComponentActivity"
50-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d955b063841d8ced20f95707b5602e\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
51            android:exported="true" />
51-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d955b063841d8ced20f95707b5602e\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
52
53        <provider
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.example.myapplication.androidx-startup"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2f3902c5a7b65349e24f9cb6b92ec03\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2f3902c5a7b65349e24f9cb6b92ec03\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2f3902c5a7b65349e24f9cb6b92ec03\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
