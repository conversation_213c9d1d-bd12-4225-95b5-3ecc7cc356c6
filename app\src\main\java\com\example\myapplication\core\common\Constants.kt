package com.example.myapplication.core.common

/**
 * Constantes globais da aplicação
 */
object Constants {
    // Network
    const val BASE_URL = "http://10.0.2.2:3000/"
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
    
    // Database
    const val DATABASE_NAME = "myapp_database"
    const val DATABASE_VERSION = 1
    
    // Preferences
    const val PREFERENCES_NAME = "myapp_preferences"
    const val KEY_ACCESS_TOKEN = "access_token"
    const val KEY_REFRESH_TOKEN = "refresh_token"
    const val KEY_USER_ID = "user_id"
    
    // Navigation
    const val ROUTE_LOGIN = "login"
    const val ROUTE_REGISTER = "register"
    const val ROUTE_MAIN = "main"
    const val ROUTE_PROFILE = "profile"
}
