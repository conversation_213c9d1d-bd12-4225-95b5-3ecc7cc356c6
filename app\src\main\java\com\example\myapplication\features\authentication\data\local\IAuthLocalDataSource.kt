package com.example.myapplication.features.authentication.data.local

/**
 * Interface para fonte de dados local de autenticação
 * Seguindo o princípio da Inversão de Dependência (DIP)
 */
interface IAuthLocalDataSource {
    suspend fun saveAccessToken(token: String)
    suspend fun getAccessToken(): String?
    suspend fun saveRefreshToken(token: String)
    suspend fun getRefreshToken(): String?
    suspend fun saveUserId(userId: Long)
    suspend fun getUserId(): Long?
    suspend fun clearAuthData()
    suspend fun isAuthenticated(): Boolean
}

/**
 * Implementação da fonte de dados local usando SharedPreferences
 */
class AuthLocalDataSource(
    private val preferencesManager: com.example.myapplication.core.storage.IPreferencesManager
) : IAuthLocalDataSource {
    
    companion object {
        private const val KEY_ACCESS_TOKEN = "access_token"
        private const val KEY_REFRESH_TOKEN = "refresh_token"
        private const val KEY_USER_ID = "user_id"
    }
    
    override suspend fun saveAccessToken(token: String) {
        preferencesManager.saveString(KEY_ACCESS_TOKEN, token)
    }
    
    override suspend fun getAccessToken(): String? {
        return preferencesManager.getString(KEY_ACCESS_TOKEN)
    }
    
    override suspend fun saveRefreshToken(token: String) {
        preferencesManager.saveString(KEY_REFRESH_TOKEN, token)
    }
    
    override suspend fun getRefreshToken(): String? {
        return preferencesManager.getString(KEY_REFRESH_TOKEN)
    }
    
    override suspend fun saveUserId(userId: Long) {
        preferencesManager.saveLong(KEY_USER_ID, userId)
    }
    
    override suspend fun getUserId(): Long? {
        val userId = preferencesManager.getLong(KEY_USER_ID)
        return if (userId == 0L) null else userId
    }
    
    override suspend fun clearAuthData() {
        preferencesManager.remove(KEY_ACCESS_TOKEN)
        preferencesManager.remove(KEY_REFRESH_TOKEN)
        preferencesManager.remove(KEY_USER_ID)
    }
    
    override suspend fun isAuthenticated(): Boolean {
        val accessToken = getAccessToken()
        return !accessToken.isNullOrBlank()
    }
}
