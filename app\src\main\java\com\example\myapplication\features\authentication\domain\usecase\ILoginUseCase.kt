package com.example.myapplication.features.authentication.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.features.authentication.domain.model.LoginRequest
import com.example.myapplication.features.authentication.domain.model.LoginResponse
import com.example.myapplication.features.authentication.domain.repository.IAuthRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Interface para o caso de uso de login
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
interface ILoginUseCase {
    suspend operator fun invoke(usernameOrEmail: String, password: String): Flow<Resource<LoginResponse>>
}

/**
 * Implementação do caso de uso de login
 * Contém a lógica de negócio para autenticação
 */
class LoginUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) : ILoginUseCase {
    
    override suspend operator fun invoke(
        usernameOrEmail: String, 
        password: String
    ): Flow<Resource<LoginResponse>> {
        
        // Validações de entrada
        if (usernameOrEmail.isBlank()) {
            return kotlinx.coroutines.flow.flowOf(
                Resource.Error("Username ou email não pode estar vazio")
            )
        }
        
        if (password.isBlank()) {
            return kotlinx.coroutines.flow.flowOf(
                Resource.Error("Senha não pode estar vazia")
            )
        }
        
        if (password.length < 6) {
            return kotlinx.coroutines.flow.flowOf(
                Resource.Error("Senha deve ter pelo menos 6 caracteres")
            )
        }
        
        val request = LoginRequest(
            usernameOrEmail = usernameOrEmail.trim(),
            password = password
        )
        
        return authRepository.login(request)
    }
}
