package com.example.myapplication.ui.login

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.data.remote.api.RetrofitClient
import com.example.myapplication.data.repository.AuthRepositoryImpl
import com.example.myapplication.domain.useCase.LoginUseCase

/**
 * Factory para criar instâncias do LoginViewModel
 */
class LoginViewModelFactory : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(LoginViewModel::class.java)) {
            val authRepository = AuthRepositoryImpl(RetrofitClient.apiService)
            val loginUseCase = LoginUseCase(authRepository)
            return LoginViewModel(loginUseCase) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
