package com.example.myapplication.core.network

import com.example.myapplication.core.common.Resource
import retrofit2.Response

/**
 * Interface para tratamento de erros de API
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
interface IApiErrorHandler {
    fun <T> handleApiError(response: Response<T>): Resource.Error<T>
    fun handleNetworkError(throwable: Throwable): Resource.Error<Nothing>
}

/**
 * Implementação padrão do tratamento de erros de API
 */
class ApiErrorHandler : IApiErrorHandler {
    
    override fun <T> handleApiError(response: Response<T>): Resource.Error<T> {
        val errorMessage = when (response.code()) {
            400 -> "Dados inválidos"
            401 -> "Não autorizado"
            403 -> "Acesso negado"
            404 -> "Recurso não encontrado"
            409 -> "Conflito - recurso já existe"
            422 -> "Dados inválidos"
            500 -> "Erro interno do servidor"
            502 -> "Servidor indisponível"
            503 -> "Serviço temporariamente indisponível"
            else -> "Erro desconhecido: ${response.code()}"
        }
        
        return Resource.Error(errorMessage)
    }
    
    override fun handleNetworkError(throwable: Throwable): Resource.Error<Nothing> {
        val errorMessage = when (throwable) {
            is java.net.UnknownHostException -> "Sem conexão com a internet"
            is java.net.SocketTimeoutException -> "Timeout na conexão"
            is java.net.ConnectException -> "Falha na conexão"
            else -> "Erro de rede: ${throwable.localizedMessage}"
        }
        
        return Resource.Error(errorMessage)
    }
}
