1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permission required for network access -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:6:22-64
13
14    <permission
14-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed91d6823b14a2d62ff6e32e9475a580\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed91d6823b14a2d62ff6e32e9475a580\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed91d6823b14a2d62ff6e32e9475a580\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed91d6823b14a2d62ff6e32e9475a580\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed91d6823b14a2d62ff6e32e9475a580\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:8:5-29:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed91d6823b14a2d62ff6e32e9475a580\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:10:9-65
24        android:extractNativeLibs="false"
25        android:fullBackupContent="@xml/backup_rules"
25-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:11:9-54
26        android:icon="@mipmap/ic_launcher"
26-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:12:9-43
27        android:label="@string/app_name"
27-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:13:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:14:9-54
29        android:supportsRtl="true"
29-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:15:9-35
30        android:theme="@style/Theme.MyApplication" >
30-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:16:9-51
31        <activity
31-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:18:9-28:20
32            android:name="com.example.myapplication.MainActivity"
32-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:19:13-41
33            android:exported="true"
33-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:20:13-36
34            android:label="@string/app_name"
34-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:21:13-45
35            android:theme="@style/Theme.MyApplication" >
35-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:22:13-55
36            <intent-filter>
36-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:23:13-27:29
37                <action android:name="android.intent.action.MAIN" />
37-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:24:17-69
37-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:24:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:26:17-77
39-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:26:27-74
40            </intent-filter>
41        </activity>
42
43        <provider
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b293ebf65bcc01a07447f837c73a2824\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
44            android:name="androidx.startup.InitializationProvider"
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b293ebf65bcc01a07447f837c73a2824\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
45            android:authorities="com.example.myapplication.androidx-startup"
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b293ebf65bcc01a07447f837c73a2824\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
46            android:exported="false" >
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b293ebf65bcc01a07447f837c73a2824\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
47            <meta-data
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b293ebf65bcc01a07447f837c73a2824\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.emoji2.text.EmojiCompatInitializer"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b293ebf65bcc01a07447f837c73a2824\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
49                android:value="androidx.startup" />
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b293ebf65bcc01a07447f837c73a2824\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd4936223cc8bc53ba8c6f9ec859f673\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
51-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd4936223cc8bc53ba8c6f9ec859f673\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
52                android:value="androidx.startup" />
52-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd4936223cc8bc53ba8c6f9ec859f673\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
54-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
55                android:value="androidx.startup" />
55-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
56        </provider>
57
58        <receiver
58-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
59            android:name="androidx.profileinstaller.ProfileInstallReceiver"
59-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
60            android:directBootAware="false"
60-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
61            android:enabled="true"
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
62            android:exported="true"
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
63            android:permission="android.permission.DUMP" >
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
64            <intent-filter>
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
65                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
66            </intent-filter>
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
68                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
71                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
74                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c8ef37f095163007118af657e43f9a4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
75            </intent-filter>
76        </receiver>
77    </application>
78
79</manifest>
