package com.example.myapplication.core.network

/**
 * Interface para configuração de rede
 * Seguindo o princípio da Inversão de Dependência (DIP)
 */
interface INetworkConfig {
    fun getBaseUrl(): String
    fun getConnectTimeout(): Long
    fun getReadTimeout(): Long
    fun getWriteTimeout(): Long
    fun isDebugMode(): Boolean
}

/**
 * Implementação padrão da configuração de rede
 */
class NetworkConfigImpl : INetworkConfig {
    override fun getBaseUrl(): String = "http://********:3000/"
    override fun getConnectTimeout(): Long = 30L
    override fun getReadTimeout(): Long = 30L
    override fun getWriteTimeout(): Long = 30L
    override fun isDebugMode(): Boolean = true
}
