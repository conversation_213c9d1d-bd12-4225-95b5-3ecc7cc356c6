package com.example.myapplication.features.authentication.data.mapper

import com.example.myapplication.features.authentication.data.remote.dto.LoginRequestDto
import com.example.myapplication.features.authentication.data.remote.dto.LoginResponseDto
import com.example.myapplication.features.authentication.data.remote.dto.RegisterRequestDto
import com.example.myapplication.features.authentication.data.remote.dto.RegisterResponseDto
import com.example.myapplication.features.authentication.data.remote.dto.RefreshTokenResponseDto
import com.example.myapplication.features.authentication.data.remote.dto.UserDto
import com.example.myapplication.features.authentication.domain.model.LoginRequest
import com.example.myapplication.features.authentication.domain.model.LoginResponse
import com.example.myapplication.features.authentication.domain.model.RegisterRequest
import com.example.myapplication.features.authentication.domain.model.RegisterResponse
import com.example.myapplication.features.authentication.domain.model.AuthToken
import com.example.myapplication.features.authentication.domain.model.UserProfile

/**
 * Mapper para conversão entre DTOs e modelos de domínio
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
object AuthMapper {
    
    // Domain to DTO mappings
    fun LoginRequest.toDto(): LoginRequestDto {
        return LoginRequestDto(
            usernameOrEmail = this.usernameOrEmail,
            password = this.password
        )
    }
    
    fun RegisterRequest.toDto(): RegisterRequestDto {
        return RegisterRequestDto(
            username = this.username,
            email = this.email,
            password = this.password,
            fullName = this.fullName
        )
    }
    
    // DTO to Domain mappings
    fun LoginResponseDto.toDomain(): LoginResponse {
        return LoginResponse(
            accessToken = this.accessToken,
            refreshToken = this.refreshToken,
            tokenType = this.tokenType,
            expiresIn = this.expiresIn,
            user = this.user.toDomain()
        )
    }
    
    fun RegisterResponseDto.toDomain(): RegisterResponse {
        return RegisterResponse(
            accessToken = this.accessToken,
            refreshToken = this.refreshToken,
            tokenType = this.tokenType,
            expiresIn = this.expiresIn,
            user = this.user.toDomain()
        )
    }
    
    fun RefreshTokenResponseDto.toDomain(): AuthToken {
        return AuthToken(
            accessToken = this.accessToken,
            refreshToken = this.refreshToken,
            tokenType = this.tokenType,
            expiresIn = this.expiresIn
        )
    }
    
    fun UserDto.toDomain(): UserProfile {
        return UserProfile(
            id = this.id,
            username = this.username,
            email = this.email,
            fullName = this.fullName,
            avatar = this.avatar,
            isActive = this.isActive,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt
        )
    }
}
