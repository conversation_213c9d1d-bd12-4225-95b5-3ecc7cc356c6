package com.example.myapplication.features.authentication.domain.usecase

import com.example.myapplication.features.authentication.domain.repository.IAuthRepository
import javax.inject.Inject

/**
 * Interface para o caso de uso de verificação de autenticação
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
interface ICheckAuthenticationUseCase {
    suspend operator fun invoke(): Boolean
}

/**
 * Implementação do caso de uso de verificação de autenticação
 * Verifica se o usuário está autenticado
 */
class CheckAuthenticationUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) : ICheckAuthenticationUseCase {
    
    override suspend operator fun invoke(): <PERSON>olean {
        return authRepository.isAuthenticated()
    }
}
