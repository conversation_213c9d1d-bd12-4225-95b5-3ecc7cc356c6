package com.example.myapplication.core.navigation

/**
 * Interface para gerenciamento de navegação entre módulos
 * Permite navegação sem acoplamento direto entre features
 */
interface INavigationManager {
    fun navigateToLogin()
    fun navigateToRegister()
    fun navigateToMain()
    fun navigateToProfile(userId: String)
    fun navigateBack()
    fun clearBackStack()
}

/**
 * Eventos de navegação que podem ser enviados entre módulos
 */
sealed class NavigationEvent {
    object NavigateToLogin : NavigationEvent()
    object NavigateToRegister : NavigationEvent()
    object NavigateToMain : NavigationEvent()
    data class NavigateToProfile(val userId: String) : NavigationEvent()
    object NavigateBack : NavigationEvent()
    object ClearBackStack : NavigationEvent()
}

/**
 * Rotas de navegação centralizadas
 */
object NavigationRoutes {
    const val LOGIN = "login"
    const val REGISTER = "register"
    const val MAIN = "main"
    const val PROFILE = "profile/{userId}"
    
    fun profileRoute(userId: String) = "profile/$userId"
}
