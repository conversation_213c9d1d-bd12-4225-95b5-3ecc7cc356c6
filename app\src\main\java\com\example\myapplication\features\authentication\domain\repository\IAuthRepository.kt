package com.example.myapplication.features.authentication.domain.repository

import com.example.myapplication.core.common.Resource
import com.example.myapplication.features.authentication.domain.model.LoginRequest
import com.example.myapplication.features.authentication.domain.model.LoginResponse
import com.example.myapplication.features.authentication.domain.model.RegisterRequest
import com.example.myapplication.features.authentication.domain.model.RegisterResponse
import com.example.myapplication.features.authentication.domain.model.AuthToken
import kotlinx.coroutines.flow.Flow

/**
 * Interface do repositório de autenticação
 * Seguindo o princípio da Inversão de Dependência (DIP)
 * Define o contrato para operações de autenticação
 */
interface IAuthRepository {
    
    /**
     * Realiza login do usuário
     */
    suspend fun login(request: LoginRequest): Flow<Resource<LoginResponse>>
    
    /**
     * Realiza registro de novo usuário
     */
    suspend fun register(request: RegisterRequest): Flow<Resource<RegisterResponse>>
    
    /**
     * Atualiza token de acesso usando refresh token
     */
    suspend fun refreshToken(refreshToken: String): Flow<Resource<AuthToken>>
    
    /**
     * Realiza logout do usuário
     */
    suspend fun logout(): Flow<Resource<Unit>>
    
    /**
     * Verifica se o usuário está autenticado
     */
    suspend fun isAuthenticated(): Boolean
    
    /**
     * Obtém token de acesso atual
     */
    suspend fun getAccessToken(): String?
    
    /**
     * Obtém refresh token atual
     */
    suspend fun getRefreshToken(): String?
    
    /**
     * Salva tokens de autenticação
     */
    suspend fun saveTokens(accessToken: String, refreshToken: String)
    
    /**
     * Remove tokens de autenticação
     */
    suspend fun clearTokens()
}
