package com.example.myapplication.core.storage

import android.content.SharedPreferences
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Implementação do gerenciador de preferências usando SharedPreferences
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
class PreferencesManagerImpl @Inject constructor(
    private val sharedPreferences: SharedPreferences
) : IPreferencesManager {
    
    override suspend fun saveString(key: String, value: String) = withContext(Dispatchers.IO) {
        sharedPreferences.edit().putString(key, value).apply()
    }
    
    override suspend fun getString(key: String): String? = withContext(Dispatchers.IO) {
        sharedPreferences.getString(key, null)
    }
    
    override suspend fun saveLong(key: String, value: Long) = withContext(Dispatchers.IO) {
        sharedPreferences.edit().putLong(key, value).apply()
    }
    
    override suspend fun getLong(key: String): Long = withContext(Dispatchers.IO) {
        sharedPreferences.getLong(key, 0L)
    }
    
    override suspend fun saveBoolean(key: String, value: Boolean) = withContext(Dispatchers.IO) {
        sharedPreferences.edit().putBoolean(key, value).apply()
    }
    
    override suspend fun getBoolean(key: String): Boolean = withContext(Dispatchers.IO) {
        sharedPreferences.getBoolean(key, false)
    }
    
    override suspend fun remove(key: String) = withContext(Dispatchers.IO) {
        sharedPreferences.edit().remove(key).apply()
    }
    
    override suspend fun clear() = withContext(Dispatchers.IO) {
        sharedPreferences.edit().clear().apply()
    }
}
