package com.example.myapplication.ui.login

import com.example.myapplication.domain.model.LoginResponse

/**
 * Estado da UI da tela de login
 */
data class LoginUiState(
    val usernameOrEmail: String = "",
    val password: String = "",
    val isLoading: <PERSON><PERSON>an = false,
    val isPasswordVisible: Boolean = false,
    val loginResult: LoginResult? = null,
    val errorMessage: String? = null
)

/**
 * Resultado do login
 */
sealed class LoginResult {
    data class Success(val loginResponse: LoginResponse) : LoginResult()
    data class Error(val message: String) : LoginResult()
}

/**
 * Eventos da tela de login
 */



sealed class LoginEvent {
    data class UsernameOrEmailChanged(val value: String) : LoginEvent()
    data class PasswordChanged(val value: String) : LoginEvent()
    object TogglePasswordVisibility : LoginEvent()
    object Login : LoginEvent()
    object ClearError : LoginEvent()
}
