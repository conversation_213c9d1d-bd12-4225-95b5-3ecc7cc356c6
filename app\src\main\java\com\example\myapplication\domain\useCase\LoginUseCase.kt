package com.example.myapplication.domain.useCase

import com.example.myapplication.domain.model.LoginRequest
import com.example.myapplication.domain.model.LoginResponse
import com.example.myapplication.domain.model.Resource
import com.example.myapplication.domain.repository.AuthRepository

class LoginUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(usernameOrEmail: String, password: String): Resource<LoginResponse> {
        when {
            usernameOrEmail.isBlank() -> return Resource.Error("Nome de usuário ou email é obrigatório")
            password.isBlank() -> return Resource.Error("Senha é obrigatória")
            password.length < 6 -> return Resource.Error("Senha deve ter pelo menos 6 caracteres")
        }

        val loginRequest = LoginRequest(
            usernameOrEmail = usernameOrEmail.trim(),
            password = password
        )
        return authRepository.login(loginRequest)
    }
}
