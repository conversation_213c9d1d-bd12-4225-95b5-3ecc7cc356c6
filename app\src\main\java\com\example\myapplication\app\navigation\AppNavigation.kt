package com.example.myapplication.app.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.myapplication.core.navigation.NavigationRoutes
import com.example.myapplication.features.authentication.presentation.login.LoginScreen
import com.example.myapplication.features.authentication.presentation.login.LoginViewModel
import com.example.myapplication.features.authentication.presentation.register.RegisterScreen
import com.example.myapplication.features.authentication.presentation.register.RegisterViewModel
import com.example.myapplication.features.main.presentation.MainScreen

/**
 * Navegação principal da aplicação
 * Coordena a navegação entre diferentes módulos
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
@Composable
fun AppNavigation(
    navController: NavHostController,
    startDestination: String = NavigationRoutes.LOGIN
) {
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Tela de Login
        composable(NavigationRoutes.LOGIN) {
            val viewModel: LoginViewModel = hiltViewModel()
            val uiState by viewModel.uiState.collectAsState()
            
            LoginScreen(
                uiState = uiState,
                onEvent = viewModel::onEvent,
                onNavigateToRegister = {
                    navController.navigate(NavigationRoutes.REGISTER)
                },
                onNavigateToMain = {
                    navController.navigate(NavigationRoutes.MAIN) {
                        popUpTo(NavigationRoutes.LOGIN) { inclusive = true }
                    }
                }
            )
        }
        
        // Tela de Registro
        composable(NavigationRoutes.REGISTER) {
            val viewModel: RegisterViewModel = hiltViewModel()
            val uiState by viewModel.uiState.collectAsState()
            
            RegisterScreen(
                uiState = uiState,
                onEvent = viewModel::onEvent,
                onNavigateToLogin = {
                    navController.popBackStack()
                },
                onNavigateToMain = {
                    navController.navigate(NavigationRoutes.MAIN) {
                        popUpTo(NavigationRoutes.LOGIN) { inclusive = true }
                    }
                }
            )
        }
        
        // Tela Principal
        composable(NavigationRoutes.MAIN) {
            MainScreen(
                onNavigateToLogin = {
                    navController.navigate(NavigationRoutes.LOGIN) {
                        popUpTo(0) { inclusive = true }
                    }
                }
            )
        }
        
        // Tela de Perfil
        composable(NavigationRoutes.PROFILE) { backStackEntry ->
            val userId = backStackEntry.arguments?.getString("userId") ?: ""
            // TODO: Implementar ProfileScreen
            // ProfileScreen(userId = userId)
        }
    }
}
