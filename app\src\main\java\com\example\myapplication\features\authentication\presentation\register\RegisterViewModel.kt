package com.example.myapplication.features.authentication.presentation.register

import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.base.BaseViewModel
import com.example.myapplication.core.base.UiEvent
import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.common.isValidEmail
import com.example.myapplication.core.common.isValidPassword
import com.example.myapplication.core.common.isValidUsername
import com.example.myapplication.features.authentication.domain.usecase.AuthUseCases
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel para a tela de registro
 * Seguindo o padrão MVVM e princípios SOLID
 */
class RegisterViewModel @Inject constructor(
    private val authUseCases: AuthUseCases
) : BaseViewModel() {
    
    private val _uiState = MutableStateFlow(RegisterUiState())
    val uiState: StateFlow<RegisterUiState> = _uiState.asStateFlow()
    
    fun onEvent(event: RegisterUiEvent) {
        when (event) {
            is RegisterUiEvent.UsernameChanged -> {
                _uiState.value = _uiState.value.copy(
                    username = event.value,
                    isUsernameError = false,
                    usernameErrorMessage = null,
                    errorMessage = null
                )
            }
            
            is RegisterUiEvent.EmailChanged -> {
                _uiState.value = _uiState.value.copy(
                    email = event.value,
                    isEmailError = false,
                    emailErrorMessage = null,
                    errorMessage = null
                )
            }
            
            is RegisterUiEvent.PasswordChanged -> {
                _uiState.value = _uiState.value.copy(
                    password = event.value,
                    isPasswordError = false,
                    passwordErrorMessage = null,
                    errorMessage = null
                )
            }
            
            is RegisterUiEvent.ConfirmPasswordChanged -> {
                _uiState.value = _uiState.value.copy(
                    confirmPassword = event.value,
                    isConfirmPasswordError = false,
                    confirmPasswordErrorMessage = null,
                    errorMessage = null
                )
            }
            
            is RegisterUiEvent.FullNameChanged -> {
                _uiState.value = _uiState.value.copy(
                    fullName = event.value,
                    isFullNameError = false,
                    fullNameErrorMessage = null,
                    errorMessage = null
                )
            }
            
            RegisterUiEvent.TogglePasswordVisibility -> {
                _uiState.value = _uiState.value.copy(
                    isPasswordVisible = !_uiState.value.isPasswordVisible
                )
            }
            
            RegisterUiEvent.ToggleConfirmPasswordVisibility -> {
                _uiState.value = _uiState.value.copy(
                    isConfirmPasswordVisible = !_uiState.value.isConfirmPasswordVisible
                )
            }
            
            RegisterUiEvent.RegisterClicked -> {
                performRegister()
            }
            
            RegisterUiEvent.NavigateToLogin -> {
                sendUiEvent(UiEvent.Navigate("login"))
            }
            
            RegisterUiEvent.ClearError -> {
                _uiState.value = _uiState.value.copy(
                    errorMessage = null,
                    registerResult = null
                )
            }
        }
    }
    
    private fun performRegister() {
        val currentState = _uiState.value
        
        // Validações locais
        val validationErrors = validateRegisterInput(
            currentState.username,
            currentState.email,
            currentState.password,
            currentState.confirmPassword,
            currentState.fullName
        )
        
        if (validationErrors.isNotEmpty()) {
            _uiState.value = currentState.copy(
                isUsernameError = validationErrors.containsKey("username"),
                usernameErrorMessage = validationErrors["username"],
                isEmailError = validationErrors.containsKey("email"),
                emailErrorMessage = validationErrors["email"],
                isPasswordError = validationErrors.containsKey("password"),
                passwordErrorMessage = validationErrors["password"],
                isConfirmPasswordError = validationErrors.containsKey("confirmPassword"),
                confirmPasswordErrorMessage = validationErrors["confirmPassword"],
                isFullNameError = validationErrors.containsKey("fullName"),
                fullNameErrorMessage = validationErrors["fullName"]
            )
            return
        }
        
        // Realizar registro
        viewModelScope.launch {
            authUseCases.registerUseCase(
                currentState.username,
                currentState.email,
                currentState.password,
                currentState.fullName.takeIf { it.isNotBlank() }
            ).collect { resource ->
                when (resource) {
                    is Resource.Loading -> {
                        _uiState.value = currentState.copy(isLoading = true)
                    }
                    
                    is Resource.Success -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            registerResult = RegisterResult.Success(resource.data)
                        )
                        sendUiEvent(UiEvent.ShowToast("Conta criada com sucesso!"))
                    }
                    
                    is Resource.Error -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            errorMessage = resource.message,
                            registerResult = RegisterResult.Error(resource.message ?: "Erro desconhecido")
                        )
                        sendUiEvent(UiEvent.ShowError(resource.message ?: "Erro no registro"))
                    }
                }
            }
        }
    }
    
    private fun validateRegisterInput(
        username: String,
        email: String,
        password: String,
        confirmPassword: String,
        fullName: String
    ): Map<String, String> {
        val errors = mutableMapOf<String, String>()
        
        if (username.isBlank()) {
            errors["username"] = "Username é obrigatório"
        } else if (!username.isValidUsername()) {
            errors["username"] = "Username deve ter pelo menos 3 caracteres e conter apenas letras, números e underscore"
        }
        
        if (email.isBlank()) {
            errors["email"] = "Email é obrigatório"
        } else if (!email.isValidEmail()) {
            errors["email"] = "Email inválido"
        }
        
        if (password.isBlank()) {
            errors["password"] = "Senha é obrigatória"
        } else if (!password.isValidPassword()) {
            errors["password"] = "Senha deve ter pelo menos 6 caracteres"
        }
        
        if (confirmPassword.isBlank()) {
            errors["confirmPassword"] = "Confirmação de senha é obrigatória"
        } else if (password != confirmPassword) {
            errors["confirmPassword"] = "Senhas não coincidem"
        }
        
        return errors
    }
}
