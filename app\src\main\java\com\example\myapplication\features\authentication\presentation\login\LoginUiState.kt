package com.example.myapplication.features.authentication.presentation.login

import com.example.myapplication.features.authentication.domain.model.LoginResponse

/**
 * Estado da UI da tela de login
 * Seguindo o padrão MVVM e princípio da Responsabilidade Única (SRP)
 */
data class LoginUiState(
    val usernameOrEmail: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val isPasswordVisible: Boolean = false,
    val loginResult: LoginResult? = null,
    val errorMessage: String? = null,
    val isUsernameOrEmailError: Boolean = false,
    val isPasswordError: Boolean = false,
    val usernameOrEmailErrorMessage: String? = null,
    val passwordErrorMessage: String? = null
)

/**
 * Resultado do login
 */
sealed class LoginResult {
    data class Success(val loginResponse: LoginResponse) : LoginResult()
    data class Error(val message: String) : LoginResult()
}

/**
 * Eventos da tela de login
 */
sealed class LoginUiEvent {
    data class UsernameOrEmailChanged(val value: String) : LoginUiEvent()
    data class PasswordChanged(val value: String) : LoginUiEvent()
    object TogglePasswordVisibility : LoginUiEvent()
    object LoginClicked : LoginUiEvent()
    object NavigateToRegister : LoginUiEvent()
    object ShowNetworkTest : LoginUiEvent()
    object ClearError : LoginUiEvent()
}
