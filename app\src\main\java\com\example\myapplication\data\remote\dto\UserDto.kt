package com.example.myapplication.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * Data Transfer Object for User data from API
 */
data class UserDto(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("username")
    val username: String,
    
    @SerializedName("email")
    val email: String,
    
    @SerializedName("full_name")
    val fullName: String? = null,
    
    @SerializedName("profile_picture")
    val profilePicture: String? = null,
    
    @SerializedName("created_at")
    val createdAt: String? = null,
    
    @SerializedName("updated_at")
    val updatedAt: String? = null,
    
    @SerializedName("is_active")
    val isActive: Boolean = true
)