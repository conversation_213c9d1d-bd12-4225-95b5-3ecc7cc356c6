package com.example.myapplication.features.authentication.data.repository

import com.example.myapplication.core.common.Resource
import com.example.myapplication.features.authentication.data.local.IAuthLocalDataSource
import com.example.myapplication.features.authentication.data.remote.IAuthRemoteDataSource
import com.example.myapplication.features.authentication.domain.model.AuthToken
import com.example.myapplication.features.authentication.domain.model.LoginRequest
import com.example.myapplication.features.authentication.domain.model.LoginResponse
import com.example.myapplication.features.authentication.domain.model.RegisterRequest
import com.example.myapplication.features.authentication.domain.model.RegisterResponse
import com.example.myapplication.features.authentication.domain.repository.IAuthRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

/**
 * Implementação do repositório de autenticação
 * Coordena entre fontes de dados local e remota
 * Seguindo o padrão Repository e princípios SOLID
 */
class AuthRepositoryImpl @Inject constructor(
    private val remoteDataSource: IAuthRemoteDataSource,
    private val localDataSource: IAuthLocalDataSource
) : IAuthRepository {
    
    override suspend fun login(request: LoginRequest): Flow<Resource<LoginResponse>> {
        return remoteDataSource.login(request).onEach { resource ->
            if (resource is Resource.Success) {
                // Salvar tokens localmente após login bem-sucedido
                saveTokens(resource.data.accessToken, resource.data.refreshToken)
                localDataSource.saveUserId(resource.data.user.id)
            }
        }
    }
    
    override suspend fun register(request: RegisterRequest): Flow<Resource<RegisterResponse>> {
        return remoteDataSource.register(request).onEach { resource ->
            if (resource is Resource.Success) {
                // Salvar tokens localmente após registro bem-sucedido
                saveTokens(resource.data.accessToken, resource.data.refreshToken)
                localDataSource.saveUserId(resource.data.user.id)
            }
        }
    }
    
    override suspend fun refreshToken(refreshToken: String): Flow<Resource<AuthToken>> {
        return remoteDataSource.refreshToken(refreshToken).onEach { resource ->
            if (resource is Resource.Success) {
                // Atualizar tokens localmente
                saveTokens(resource.data.accessToken, resource.data.refreshToken)
            }
        }
    }
    
    override suspend fun logout(): Flow<Resource<Unit>> = flow {
        emit(Resource.Loading())
        
        try {
            // Tentar fazer logout no servidor
            remoteDataSource.logout().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        // Limpar dados locais independentemente do resultado do servidor
                        clearTokens()
                        emit(Resource.Success(Unit))
                    }
                    is Resource.Error -> {
                        // Mesmo com erro no servidor, limpar dados locais
                        clearTokens()
                        emit(Resource.Success(Unit))
                    }
                    is Resource.Loading -> {
                        emit(Resource.Loading())
                    }
                }
            }
        } catch (e: Exception) {
            // Em caso de erro, ainda assim limpar dados locais
            clearTokens()
            emit(Resource.Success(Unit))
        }
    }
    
    override suspend fun isAuthenticated(): Boolean {
        return localDataSource.isAuthenticated()
    }
    
    override suspend fun getAccessToken(): String? {
        return localDataSource.getAccessToken()
    }
    
    override suspend fun getRefreshToken(): String? {
        return localDataSource.getRefreshToken()
    }
    
    override suspend fun saveTokens(accessToken: String, refreshToken: String) {
        localDataSource.saveAccessToken(accessToken)
        localDataSource.saveRefreshToken(refreshToken)
    }
    
    override suspend fun clearTokens() {
        localDataSource.clearAuthData()
    }
}
