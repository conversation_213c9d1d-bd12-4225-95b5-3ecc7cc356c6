package com.example.myapplication.features.authentication.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * DTOs para comunicação com a API de autenticação
 * Separados dos modelos de domínio seguindo Clean Architecture
 */

/**
 * DTO para requisição de login
 */
data class LoginRequestDto(
    @SerializedName("usernameOrEmail")
    val usernameOrEmail: String,
    
    @SerializedName("password")
    val password: String
)

/**
 * DTO para resposta de login
 */
data class LoginResponseDto(
    @SerializedName("access_token")
    val accessToken: String,
    
    @SerializedName("refresh_token")
    val refreshToken: String,
    
    @SerializedName("token_type")
    val tokenType: String,
    
    @SerializedName("expires_in")
    val expiresIn: Long,
    
    @SerializedName("user")
    val user: UserDto
)

/**
 * DTO para requisição de registro
 */
data class RegisterRequestDto(
    @SerializedName("username")
    val username: String,
    
    @SerializedName("email")
    val email: String,
    
    @SerializedName("password")
    val password: String,
    
    @SerializedName("fullName")
    val fullName: String? = null
)

/**
 * DTO para resposta de registro
 */
data class RegisterResponseDto(
    @SerializedName("access_token")
    val accessToken: String,
    
    @SerializedName("refresh_token")
    val refreshToken: String,
    
    @SerializedName("token_type")
    val tokenType: String,
    
    @SerializedName("expires_in")
    val expiresIn: Long,
    
    @SerializedName("user")
    val user: UserDto
)

/**
 * DTO para dados do usuário
 */
data class UserDto(
    @SerializedName("id")
    val id: Long,
    
    @SerializedName("username")
    val username: String,
    
    @SerializedName("email")
    val email: String,
    
    @SerializedName("fullName")
    val fullName: String,
    
    @SerializedName("avatar")
    val avatar: String?,
    
    @SerializedName("isActive")
    val isActive: Boolean,
    
    @SerializedName("createdAt")
    val createdAt: String,
    
    @SerializedName("updatedAt")
    val updatedAt: String
)

/**
 * DTO para refresh token
 */
data class RefreshTokenRequestDto(
    @SerializedName("refresh_token")
    val refreshToken: String
)

/**
 * DTO para resposta de refresh token
 */
data class RefreshTokenResponseDto(
    @SerializedName("access_token")
    val accessToken: String,
    
    @SerializedName("refresh_token")
    val refreshToken: String,
    
    @SerializedName("token_type")
    val tokenType: String,
    
    @SerializedName("expires_in")
    val expiresIn: Long
)
