package com.example.myapplication.features.authentication.presentation.login

import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.base.BaseViewModel
import com.example.myapplication.core.base.UiEvent
import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.common.isValidEmail
import com.example.myapplication.core.common.isValidPassword
import com.example.myapplication.core.common.isValidUsername
import com.example.myapplication.features.authentication.domain.usecase.AuthUseCases
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel para a tela de login
 * Seguindo o padrão MVVM e princípios SOLID
 */
class LoginViewModel @Inject constructor(
    private val authUseCases: AuthUseCases
) : BaseViewModel() {
    
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()
    
    fun onEvent(event: LoginUiEvent) {
        when (event) {
            is LoginUiEvent.UsernameOrEmailChanged -> {
                _uiState.value = _uiState.value.copy(
                    usernameOrEmail = event.value,
                    isUsernameOrEmailError = false,
                    usernameOrEmailErrorMessage = null,
                    errorMessage = null
                )
            }
            
            is LoginUiEvent.PasswordChanged -> {
                _uiState.value = _uiState.value.copy(
                    password = event.value,
                    isPasswordError = false,
                    passwordErrorMessage = null,
                    errorMessage = null
                )
            }
            
            LoginUiEvent.TogglePasswordVisibility -> {
                _uiState.value = _uiState.value.copy(
                    isPasswordVisible = !_uiState.value.isPasswordVisible
                )
            }
            
            LoginUiEvent.LoginClicked -> {
                performLogin()
            }
            
            LoginUiEvent.NavigateToRegister -> {
                sendUiEvent(UiEvent.Navigate("register"))
            }
            
            LoginUiEvent.ShowNetworkTest -> {
                sendUiEvent(UiEvent.Navigate("network_test"))
            }
            
            LoginUiEvent.ClearError -> {
                _uiState.value = _uiState.value.copy(
                    errorMessage = null,
                    loginResult = null
                )
            }
        }
    }
    
    private fun performLogin() {
        val currentState = _uiState.value
        
        // Validações locais
        val validationErrors = validateLoginInput(
            currentState.usernameOrEmail,
            currentState.password
        )
        
        if (validationErrors.isNotEmpty()) {
            _uiState.value = currentState.copy(
                isUsernameOrEmailError = validationErrors.containsKey("usernameOrEmail"),
                usernameOrEmailErrorMessage = validationErrors["usernameOrEmail"],
                isPasswordError = validationErrors.containsKey("password"),
                passwordErrorMessage = validationErrors["password"]
            )
            return
        }
        
        // Realizar login
        viewModelScope.launch {
            authUseCases.loginUseCase(
                currentState.usernameOrEmail,
                currentState.password
            ).collect { resource ->
                when (resource) {
                    is Resource.Loading -> {
                        _uiState.value = currentState.copy(isLoading = true)
                    }
                    
                    is Resource.Success -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            loginResult = LoginResult.Success(resource.data)
                        )
                        sendUiEvent(UiEvent.ShowToast("Login realizado com sucesso!"))
                    }
                    
                    is Resource.Error -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            errorMessage = resource.message,
                            loginResult = LoginResult.Error(resource.message ?: "Erro desconhecido")
                        )
                        sendUiEvent(UiEvent.ShowError(resource.message ?: "Erro no login"))
                    }
                }
            }
        }
    }
    
    private fun validateLoginInput(
        usernameOrEmail: String,
        password: String
    ): Map<String, String> {
        val errors = mutableMapOf<String, String>()
        
        if (usernameOrEmail.isBlank()) {
            errors["usernameOrEmail"] = "Username ou email é obrigatório"
        } else {
            val isEmail = usernameOrEmail.contains("@")
            if (isEmail && !usernameOrEmail.isValidEmail()) {
                errors["usernameOrEmail"] = "Email inválido"
            } else if (!isEmail && !usernameOrEmail.isValidUsername()) {
                errors["usernameOrEmail"] = "Username inválido"
            }
        }
        
        if (password.isBlank()) {
            errors["password"] = "Senha é obrigatória"
        } else if (!password.isValidPassword()) {
            errors["password"] = "Senha deve ter pelo menos 6 caracteres"
        }
        
        return errors
    }
}
