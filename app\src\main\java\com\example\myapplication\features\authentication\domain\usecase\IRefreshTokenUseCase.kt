package com.example.myapplication.features.authentication.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.features.authentication.domain.model.AuthToken
import com.example.myapplication.features.authentication.domain.repository.IAuthRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject

/**
 * Interface para o caso de uso de refresh token
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
interface IRefreshTokenUseCase {
    suspend operator fun invoke(): Flow<Resource<AuthToken>>
}

/**
 * Implementação do caso de uso de refresh token
 * Atualiza o token de acesso usando o refresh token
 */
class RefreshTokenUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) : IRefreshTokenUseCase {
    
    override suspend operator fun invoke(): Flow<Resource<AuthToken>> {
        val refreshToken = authRepository.getRefreshToken()
        
        if (refreshToken.isNullOrBlank()) {
            return flowOf(Resource.Error("Refresh token não encontrado"))
        }
        
        return authRepository.refreshToken(refreshToken)
    }
}
