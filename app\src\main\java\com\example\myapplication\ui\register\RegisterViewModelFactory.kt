package com.example.myapplication.ui.register

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.data.remote.api.RetrofitClient
import com.example.myapplication.data.repository.AuthRepositoryImpl
import com.example.myapplication.domain.useCase.RegisterUseCase

/**
 * Factory para criar instâncias do RegisterViewModel
 */
class RegisterViewModelFactory : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(RegisterViewModel::class.java)) {
            val authRepository = AuthRepositoryImpl(RetrofitClient.apiService)
            val registerUseCase = RegisterUseCase(authRepository)
            return RegisterViewModel(registerUseCase) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
