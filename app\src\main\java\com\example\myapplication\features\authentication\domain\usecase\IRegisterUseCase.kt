package com.example.myapplication.features.authentication.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.common.isValidEmail
import com.example.myapplication.core.common.isValidPassword
import com.example.myapplication.core.common.isValidUsername
import com.example.myapplication.features.authentication.domain.model.RegisterRequest
import com.example.myapplication.features.authentication.domain.model.RegisterResponse
import com.example.myapplication.features.authentication.domain.repository.IAuthRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject

/**
 * Interface para o caso de uso de registro
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
interface IRegisterUseCase {
    suspend operator fun invoke(
        username: String,
        email: String,
        password: String,
        fullName: String?
    ): Flow<Resource<RegisterResponse>>
}

/**
 * Implementação do caso de uso de registro
 * Contém a lógica de negócio para criação de conta
 */
class RegisterUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) : IRegisterUseCase {
    
    override suspend operator fun invoke(
        username: String,
        email: String,
        password: String,
        fullName: String?
    ): Flow<Resource<RegisterResponse>> {
        
        // Validações de entrada
        if (username.isBlank()) {
            return flowOf(Resource.Error("Username não pode estar vazio"))
        }
        
        if (!username.isValidUsername()) {
            return flowOf(Resource.Error("Username deve ter pelo menos 3 caracteres e conter apenas letras, números e underscore"))
        }
        
        if (email.isBlank()) {
            return flowOf(Resource.Error("Email não pode estar vazio"))
        }
        
        if (!email.isValidEmail()) {
            return flowOf(Resource.Error("Email inválido"))
        }
        
        if (password.isBlank()) {
            return flowOf(Resource.Error("Senha não pode estar vazia"))
        }
        
        if (!password.isValidPassword()) {
            return flowOf(Resource.Error("Senha deve ter pelo menos 6 caracteres"))
        }
        
        val request = RegisterRequest(
            username = username.trim(),
            email = email.trim(),
            password = password,
            fullName = fullName?.trim()
        )
        
        return authRepository.register(request)
    }
}
