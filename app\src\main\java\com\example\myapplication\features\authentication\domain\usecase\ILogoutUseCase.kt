package com.example.myapplication.features.authentication.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.features.authentication.domain.repository.IAuthRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Interface para o caso de uso de logout
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
interface ILogoutUseCase {
    suspend operator fun invoke(): Flow<Resource<Unit>>
}

/**
 * Implementação do caso de uso de logout
 * Contém a lógica de negócio para logout
 */
class LogoutUseCase @Inject constructor(
    private val authRepository: IAuthRepository
) : ILogoutUseCase {
    
    override suspend operator fun invoke(): Flow<Resource<Unit>> {
        return authRepository.logout()
    }
}
