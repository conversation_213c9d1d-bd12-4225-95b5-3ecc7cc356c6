<dependencies>
  <compile
      roots="androidx.compose.material3:material3-android:1.3.0@aar,androidx.compose.ui:ui-util-android:1.7.0@aar,androidx.compose.ui:ui-unit-android:1.7.0@aar,androidx.compose.ui:ui-text-android:1.7.0@aar,androidx.compose.foundation:foundation-layout-android:1.7.0@aar,androidx.compose.material:material-ripple-android:1.7.0@aar,androidx.compose.foundation:foundation-android:1.7.0@aar,androidx.compose.animation:animation-core-android:1.7.0@aar,androidx.compose.animation:animation-android:1.7.0@aar,androidx.compose.ui:ui-geometry-android:1.7.0@aar,androidx.compose.ui:ui-tooling-data-android:1.7.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar,androidx.compose.ui:ui-graphics-android:1.7.0@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3@aar,androidx.compose.material:material-icons-extended-android:1.7.0@aar,androidx.compose.material:material-icons-core-android:1.7.0@aar,androidx.compose.ui:ui-android:1.7.0@aar,androidx.compose.ui:ui-tooling-android:1.7.0@aar,androidx.compose.ui:ui-test-manifest:1.7.0@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.core:core-ktx:1.13.1@aar,androidx.compose.runtime:runtime-saveable-android:1.7.0@aar,androidx.compose.runtime:runtime-android:1.7.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-jvm:1.4.0@jar,androidx.annotation:annotation-jvm:1.8.0@jar,androidx.annotation:annotation-experimental:1.4.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,org.jetbrains:annotations:23.0.0@jar,com.squareup.okhttp3:okhttp:3.14.9@jar,com.squareup.okio:okio:1.17.2@jar,com.google.code.gson:gson:2.8.5@jar">
    <dependency
        name="androidx.compose.material3:material3-android:1.3.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.0@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.0@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.14.9@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:1.17.2@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="com.google.code.gson:gson:2.8.5@jar"
        simpleName="com.google.code.gson:gson"/>
  </compile>
  <package
      roots="androidx.compose.material3:material3-android:1.3.0@aar,androidx.compose.material:material-android:1.7.0@aar,androidx.compose.material:material-ripple-android:1.7.0@aar,androidx.compose.foundation:foundation-android:1.7.0@aar,androidx.compose.animation:animation-android:1.7.0@aar,androidx.compose.foundation:foundation-layout-android:1.7.0@aar,androidx.compose.animation:animation-core-android:1.7.0@aar,androidx.compose.ui:ui-tooling-data-android:1.7.0@aar,androidx.compose.ui:ui-unit-android:1.7.0@aar,androidx.compose.ui:ui-geometry-android:1.7.0@aar,androidx.compose.ui:ui-util-android:1.7.0@aar,androidx.compose.ui:ui-text-android:1.7.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar,androidx.compose.ui:ui-tooling-android:1.7.0@aar,androidx.compose.ui:ui-graphics-android:1.7.0@aar,androidx.compose.ui:ui-test-manifest:1.7.0@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.compose.material:material-icons-extended-android:1.7.0@aar,androidx.compose.material:material-icons-core-android:1.7.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-process:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-common-java8:2.8.3@jar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3@aar,androidx.compose.ui:ui-android:1.7.0@aar,androidx.compose.runtime:runtime-saveable-android:1.7.0@aar,androidx.compose.runtime:runtime-android:1.7.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.collection:collection-ktx:1.4.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-jvm:1.4.0@jar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.0@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,org.jetbrains:annotations:23.0.0@jar,com.squareup.okhttp3:okhttp:3.14.9@jar,com.google.code.gson:gson:2.8.5@jar,com.squareup.okio:okio:1.17.2@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name="androidx.compose.material3:material3-android:1.3.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.0@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.0@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.14.9@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.google.code.gson:gson:2.8.5@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.squareup.okio:okio:1.17.2@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
