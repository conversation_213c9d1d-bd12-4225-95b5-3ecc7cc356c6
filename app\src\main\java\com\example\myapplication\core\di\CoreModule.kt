package com.example.myapplication.core.di

import android.content.Context
import android.content.SharedPreferences
import com.example.myapplication.core.network.ApiErrorHandler
import com.example.myapplication.core.network.IApiErrorHandler
import com.example.myapplication.core.network.INetworkConfig
import com.example.myapplication.core.network.NetworkConfigImpl
import com.example.myapplication.core.storage.IPreferencesManager
import com.example.myapplication.core.storage.ITokenManager
import com.example.myapplication.core.storage.PreferencesManagerImpl
import com.example.myapplication.core.storage.TokenManagerImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Módulo de DI para componentes core/shared
 * Seguindo o princípio da Inversão de Dependência (DIP)
 */
@Module
@InstallIn(SingletonComponent::class)
object CoreModule {
    
    @Provides
    @Singleton
    fun provideSharedPreferences(
        @ApplicationContext context: Context
    ): SharedPreferences {
        return context.getSharedPreferences("myapp_preferences", Context.MODE_PRIVATE)
    }
    
    @Provides
    @Singleton
    fun providePreferencesManager(
        sharedPreferences: SharedPreferences
    ): IPreferencesManager {
        return PreferencesManagerImpl(sharedPreferences)
    }
    
    @Provides
    @Singleton
    fun provideTokenManager(
        preferencesManager: IPreferencesManager
    ): ITokenManager {
        return TokenManagerImpl(preferencesManager)
    }
    
    @Provides
    @Singleton
    fun provideNetworkConfig(): INetworkConfig {
        return NetworkConfigImpl()
    }
    
    @Provides
    @Singleton
    fun provideApiErrorHandler(): IApiErrorHandler {
        return ApiErrorHandler()
    }
}
