package com.example.myapplication

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.ui.login.LoginResult
import com.example.myapplication.ui.login.LoginScreen
import com.example.myapplication.ui.login.LoginViewModel
import com.example.myapplication.ui.login.LoginViewModelFactory
import com.example.myapplication.ui.register.RegisterScreen
import com.example.myapplication.ui.register.RegisterViewModel
import com.example.myapplication.ui.register.RegisterViewModelFactory
import com.example.myapplication.ui.login.NetworkTestScreen
import com.example.myapplication.ui.theme.MyApplicationTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            MyApplicationTheme {
                val isLoggedIn = remember { mutableStateOf(false) }
                val showNetworkTest = remember { mutableStateOf(false) }
                val showRegister = remember { mutableStateOf(false) }

                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    when {
                        showNetworkTest.value -> {
                            NetworkTestScreen()
                        }
                        showRegister.value && !isLoggedIn.value -> {
                            val registerViewModel: RegisterViewModel = viewModel(
                                factory = RegisterViewModelFactory()
                            )

                            RegisterScreen(
                                viewModel = registerViewModel,
                                onRegisterSuccess = { result ->
                                    // Mostrar toast de sucesso
                                    Toast.makeText(
                                        this@MainActivity,
                                        "Conta criada com sucesso! Bem-vindo, ${result.registerResponse.user.fullName}",
                                        Toast.LENGTH_LONG
                                    ).show()

                                    // Marcar como logado
                                    isLoggedIn.value = true
                                    showRegister.value = false
                                },
                                onNavigateToLogin = {
                                    showRegister.value = false
                                },
                                modifier = Modifier.padding(innerPadding)
                            )
                        }
                        !isLoggedIn.value -> {
                            val loginViewModel: LoginViewModel = viewModel(
                                factory = LoginViewModelFactory()
                            )

                            LoginScreen(
                                viewModel = loginViewModel,
                                onLoginSuccess = { result ->
                                    // Mostrar toast de sucesso
                                    Toast.makeText(
                                        this@MainActivity,
                                        "Login realizado com sucesso! Bem-vindo, ${result.loginResponse.user.fullName}",
                                        Toast.LENGTH_LONG
                                    ).show()

                                    // Marcar como logado
                                    isLoggedIn.value = true
                                },
                                onShowNetworkTest = {
                                    showNetworkTest.value = true
                                },
                                onNavigateToRegister = {
                                    showRegister.value = true
                                },
                                modifier = Modifier.padding(innerPadding)
                            )
                        }
                        else -> {
                            // Tela principal após login
                            Greeting(
                                name = "Usuário Logado",
                                modifier = Modifier.padding(innerPadding)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun Greeting(name: String, modifier: Modifier = Modifier) {
    Text(
        text = "Hello $name!",
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    MyApplicationTheme {
        Greeting("Android")
    }
}