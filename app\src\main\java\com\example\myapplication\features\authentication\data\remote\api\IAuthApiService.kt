package com.example.myapplication.features.authentication.data.remote.api

import com.example.myapplication.features.authentication.data.remote.dto.LoginRequestDto
import com.example.myapplication.features.authentication.data.remote.dto.LoginResponseDto
import com.example.myapplication.features.authentication.data.remote.dto.RegisterRequestDto
import com.example.myapplication.features.authentication.data.remote.dto.RegisterResponseDto
import com.example.myapplication.features.authentication.data.remote.dto.RefreshTokenRequestDto
import com.example.myapplication.features.authentication.data.remote.dto.RefreshTokenResponseDto
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Interface para serviços de API de autenticação
 * Seguindo o princípio da Segregação de Interface (ISP)
 */
interface IAuthApiService {
    
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequestDto): Response<LoginResponseDto>
    
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequestDto): Response<RegisterResponseDto>
    
    @POST("auth/refresh")
    suspend fun refreshToken(@Body request: RefreshTokenRequestDto): Response<RefreshTokenResponseDto>
    
    @POST("auth/logout")
    suspend fun logout(): Response<Unit>
}
