&com.example.myapplication.MainActivity<com.example.myapplication.data.repository.AuthRepositoryImpl7com.example.myapplication.domain.model.Resource.Success5com.example.myapplication.domain.model.Resource.Error7com.example.myapplication.domain.model.Resource.Loading6com.example.myapplication.ui.login.LoginResult.Success4com.example.myapplication.ui.login.LoginResult.ErrorDcom.example.myapplication.ui.login.LoginEvent.UsernameOrEmailChanged=com.example.myapplication.ui.login.LoginEvent.PasswordChangedFcom.example.myapplication.ui.login.LoginEvent.TogglePasswordVisibility3com.example.myapplication.ui.login.LoginEvent.Login8com.example.myapplication.ui.login.LoginEvent.ClearError1com.example.myapplication.ui.login.LoginViewModel8com.example.myapplication.ui.login.LoginViewModelFactory<com.example.myapplication.ui.register.RegisterResult.Success:<EMAIL><com.example.myapplication.ui.register.RegisterEvent.Register>com.example.myapplication.ui.register.RegisterEvent.ClearErrorCcom.example.myapplication.ui.register.RegisterEvent.NavigateToLogin7com.example.myapplication.ui.register.RegisterViewModel>com.example.myapplication.ui.register.RegisterViewModelFactory                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               