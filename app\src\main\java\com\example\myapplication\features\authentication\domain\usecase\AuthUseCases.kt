package com.example.myapplication.features.authentication.domain.usecase

import javax.inject.Inject

/**
 * Agregador de todos os use cases de autenticação
 * Facilita a injeção de dependências e organização
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
data class AuthUseCases @Inject constructor(
    val loginUseCase: ILoginUseCase,
    val registerUseCase: IRegisterUseCase,
    val logoutUseCase: ILogoutUseCase,
    val checkAuthenticationUseCase: ICheckAuthenticationUseCase,
    val refreshTokenUseCase: IRefreshTokenUseCase
)
