package com.example.myapplication.core.storage

/**
 * Interface para gerenciamento de preferências/storage local
 * Seguindo o princípio da Inversão de Dependência (DIP)
 */
interface IPreferencesManager {
    suspend fun saveString(key: String, value: String)
    suspend fun getString(key: String): String?
    suspend fun saveLong(key: String, value: Long)
    suspend fun getLong(key: String): Long
    suspend fun saveBoolean(key: String, value: Boolean)
    suspend fun getBoolean(key: String): Boolean
    suspend fun remove(key: String)
    suspend fun clear()
}

/**
 * Interface específica para gerenciamento de tokens de autenticação
 * Seguindo o princípio da Segregação de Interface (ISP)
 */
interface ITokenManager {
    suspend fun saveAccessToken(token: String)
    suspend fun getAccessToken(): String?
    suspend fun saveRefreshToken(token: String)
    suspend fun getRefreshToken(): String?
    suspend fun clearTokens()
    suspend fun isTokenValid(): <PERSON><PERSON><PERSON>
}
