package com.example.myapplication.core.base

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * Interface base para ViewModels
 * Define contratos comuns para todos os ViewModels da aplicação
 */
interface IBaseViewModel {
    val uiEvents: SharedFlow<UiEvent>
    fun sendUiEvent(event: UiEvent)
}

/**
 * Implementação base para ViewModels
 * Fornece funcionalidades comuns como eventos de UI
 */
abstract class BaseViewModel : ViewModel(), IBaseViewModel {
    
    private val _uiEvents = MutableSharedFlow<UiEvent>()
    override val uiEvents: SharedFlow<UiEvent> = _uiEvents.asSharedFlow()
    
    override fun sendUiEvent(event: UiEvent) {
        _uiEvents.tryEmit(event)
    }
}

/**
 * Eventos de UI que podem ser enviados pelos ViewModels
 */
sealed class UiEvent {
    data class ShowToast(val message: String) : UiEvent()
    data class ShowError(val message: String) : UiEvent()
    data class Navigate(val route: String) : UiEvent()
    object NavigateBack : UiEvent()
    data class ShowLoading(val isLoading: Boolean) : UiEvent()
}
