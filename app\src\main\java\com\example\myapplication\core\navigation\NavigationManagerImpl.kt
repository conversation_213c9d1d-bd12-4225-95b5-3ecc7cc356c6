package com.example.myapplication.core.navigation

import androidx.navigation.NavController
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementação do gerenciador de navegação
 * Permite navegação entre módulos sem acoplamento direto
 * Seguindo o princípio da Inversão de Dependência (DIP)
 */
@Singleton
class NavigationManagerImpl @Inject constructor() : INavigationManager {
    
    private var navController: NavController? = null
    
    private val _navigationEvents = MutableSharedFlow<NavigationEvent>()
    val navigationEvents: SharedFlow<NavigationEvent> = _navigationEvents.asSharedFlow()
    
    fun setNavController(navController: NavController) {
        this.navController = navController
    }
    
    override fun navigateToLogin() {
        navController?.navigate(NavigationRoutes.LOGIN) {
            popUpTo(0) { inclusive = true }
        } ?: _navigationEvents.tryEmit(NavigationEvent.NavigateToLogin)
    }
    
    override fun navigateToRegister() {
        navController?.navigate(NavigationRoutes.REGISTER)
            ?: _navigationEvents.tryEmit(NavigationEvent.NavigateToRegister)
    }
    
    override fun navigateToMain() {
        navController?.navigate(NavigationRoutes.MAIN) {
            popUpTo(0) { inclusive = true }
        } ?: _navigationEvents.tryEmit(NavigationEvent.NavigateToMain)
    }
    
    override fun navigateToProfile(userId: String) {
        navController?.navigate(NavigationRoutes.profileRoute(userId))
            ?: _navigationEvents.tryEmit(NavigationEvent.NavigateToProfile(userId))
    }
    
    override fun navigateBack() {
        navController?.popBackStack()
            ?: _navigationEvents.tryEmit(NavigationEvent.NavigateBack)
    }
    
    override fun clearBackStack() {
        navController?.popBackStack(0, true)
            ?: _navigationEvents.tryEmit(NavigationEvent.ClearBackStack)
    }
}
