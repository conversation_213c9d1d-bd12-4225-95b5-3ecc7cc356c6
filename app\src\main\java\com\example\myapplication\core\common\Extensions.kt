package com.example.myapplication.core.common

import android.content.Context
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect

/**
 * Extensões utilitárias para a aplicação
 */

/**
 * Extensão para mostrar Toast de forma mais simples
 */
fun Context.showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(this, message, duration).show()
}

/**
 * Extensão para coletar Flow em Composables
 */
@Composable
fun <T> Flow<T>.collectAsEffect(action: (T) -> Unit) {
    val context = LocalContext.current
    LaunchedEffect(this) {
        <EMAIL> { value ->
            action(value)
        }
    }
}

/**
 * Extensão para validar email
 */
fun String.isValidEmail(): Boolean {
    return android.util.Patterns.EMAIL_ADDRESS.matcher(this).matches()
}

/**
 * Extensão para validar senha
 */
fun String.isValidPassword(): <PERSON>olean {
    return this.length >= 6
}

/**
 * Extensão para validar username
 */
fun String.isValidUsername(): Boolean {
    return this.length >= 3 && this.matches(Regex("^[a-zA-Z0-9_]+$"))
}
