package com.example.myapplication.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * Classe base para todos os DTOs de resposta da API
 */
data class BaseResponseDto<T>(
    @SerializedName("success")
    val success: <PERSON><PERSON><PERSON>,
    
    @SerializedName("message")
    val message: String? = null,
    
    @SerializedName("data")
    val data: T? = null,
    
    @SerializedName("error_code")
    val errorCode: String? = null,
    
    @SerializedName("meta")
    val meta: MetaDto? = null
)

/**
 * DTO para metadados da resposta
 */
data class MetaDto(
    @SerializedName("timestamp")
    val timestamp: String
)