package com.example.myapplication.domain.repository

import com.example.myapplication.domain.model.LoginRequest
import com.example.myapplication.domain.model.LoginResponse
import com.example.myapplication.domain.model.RegisterRequest
import com.example.myapplication.domain.model.RegisterResponse
import com.example.myapplication.domain.model.Resource

interface AuthRepository {
    suspend fun login(loginRequest: LoginRequest): Resource<LoginResponse>
    suspend fun register(registerRequest: RegisterRequest): Resource<RegisterResponse>
}
