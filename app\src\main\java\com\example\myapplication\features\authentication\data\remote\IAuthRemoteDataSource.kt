package com.example.myapplication.features.authentication.data.remote

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.network.IApiErrorHandler
import com.example.myapplication.features.authentication.data.mapper.AuthMapper.toDomain
import com.example.myapplication.features.authentication.data.mapper.AuthMapper.toDto
import com.example.myapplication.features.authentication.data.remote.api.IAuthApiService
import com.example.myapplication.features.authentication.data.remote.dto.RefreshTokenRequestDto
import com.example.myapplication.features.authentication.domain.model.AuthToken
import com.example.myapplication.features.authentication.domain.model.LoginRequest
import com.example.myapplication.features.authentication.domain.model.LoginResponse
import com.example.myapplication.features.authentication.domain.model.RegisterRequest
import com.example.myapplication.features.authentication.domain.model.RegisterResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Interface para fonte de dados remota de autenticação
 * Seguindo o princípio da Inversão de Dependência (DIP)
 */
interface IAuthRemoteDataSource {
    suspend fun login(request: LoginRequest): Flow<Resource<LoginResponse>>
    suspend fun register(request: RegisterRequest): Flow<Resource<RegisterResponse>>
    suspend fun refreshToken(refreshToken: String): Flow<Resource<AuthToken>>
    suspend fun logout(): Flow<Resource<Unit>>
}

/**
 * Implementação da fonte de dados remota usando Retrofit
 */
class AuthRemoteDataSource @Inject constructor(
    private val apiService: IAuthApiService,
    private val errorHandler: IApiErrorHandler
) : IAuthRemoteDataSource {
    
    override suspend fun login(request: LoginRequest): Flow<Resource<LoginResponse>> = flow {
        emit(Resource.Loading())
        
        try {
            val response = apiService.login(request.toDto())
            
            if (response.isSuccessful && response.body() != null) {
                val loginResponse = response.body()!!.toDomain()
                emit(Resource.Success(loginResponse))
            } else {
                emit(errorHandler.handleApiError(response))
            }
        } catch (e: Exception) {
            emit(errorHandler.handleNetworkError(e))
        }
    }
    
    override suspend fun register(request: RegisterRequest): Flow<Resource<RegisterResponse>> = flow {
        emit(Resource.Loading())
        
        try {
            val response = apiService.register(request.toDto())
            
            if (response.isSuccessful && response.body() != null) {
                val registerResponse = response.body()!!.toDomain()
                emit(Resource.Success(registerResponse))
            } else {
                emit(errorHandler.handleApiError(response))
            }
        } catch (e: Exception) {
            emit(errorHandler.handleNetworkError(e))
        }
    }
    
    override suspend fun refreshToken(refreshToken: String): Flow<Resource<AuthToken>> = flow {
        emit(Resource.Loading())
        
        try {
            val request = RefreshTokenRequestDto(refreshToken)
            val response = apiService.refreshToken(request)
            
            if (response.isSuccessful && response.body() != null) {
                val authToken = response.body()!!.toDomain()
                emit(Resource.Success(authToken))
            } else {
                emit(errorHandler.handleApiError(response))
            }
        } catch (e: Exception) {
            emit(errorHandler.handleNetworkError(e))
        }
    }
    
    override suspend fun logout(): Flow<Resource<Unit>> = flow {
        emit(Resource.Loading())
        
        try {
            val response = apiService.logout()
            
            if (response.isSuccessful) {
                emit(Resource.Success(Unit))
            } else {
                emit(errorHandler.handleApiError(response))
            }
        } catch (e: Exception) {
            emit(errorHandler.handleNetworkError(e))
        }
    }
}
