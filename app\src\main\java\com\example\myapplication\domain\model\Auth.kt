package com.example.myapplication.domain.model

/**
 * Domain model para requisição de login
 */
data class LoginRequest(
    val usernameOrEmail: String,
    val password: String
)

/**
 * Domain model para resposta de login
 */
data class LoginResponse(
    val accessToken: String,
    val refreshToken: String,
    val tokenType: String,
    val expiresIn: Long,
    val user: UserLogin
)

/**
 * Domain model para dados do usuário no login
 */
data class UserLogin(
    val id: Long,
    val username: String,
    val email: String,
    val fullName: String,
    val avatar: String?,
    val isActive: Boolean,
    val createdAt: String,
    val updatedAt: String
)

/**
 * Domain model para requisição de registro
 */
data class RegisterRequest(
    val username: String,
    val email: String,
    val password: String,
    val fullName: String? = null
)

/**
 * Domain model para resposta de registro
 */
data class RegisterResponse(
    val accessToken: String,
    val refreshToken: String,
    val tokenType: String,
    val expiresIn: Long,
    val user: UserLogin
)

/**
 * Domain model para tokens de autenticação
 */
data class AuthToken(
    val accessToken: String,
    val refreshToken: String,
    val tokenType: String,
    val expiresIn: Long
)

/**
 * Domain model para erro de autenticação
 */
data class AuthError(
    val statusCode: Int,
    val message: String,
    val error: String,
    val code: String? = null
)
