package com.example.myapplication.data.remote.api

import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * Objeto singleton responsável por criar e configurar a instância do Retrofit
 * para comunicação com a API.
 *
 * Segue o padrão Singleton para garantir que uma única instância do Retrofit
 * seja usada em todo o aplicativo, melhorando o desempenho e a consistência.
 */
object RetrofitClient {
    /**
     * URL base da API obtida da configuração
     */
    private val BASE_URL = NetworkConfig.getBaseUrl()

    /**
     * Cliente HTTP configurado com timeouts e logging
     */
    private val okHttpClient: OkHttpClient by lazy {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }

    /**
     * Instância ApiService inicializada de forma preguiçosa (lazy).
     * O delegate 'by lazy' garante que a instância do Retrofit seja criada apenas quando for acessada pela primeira vez,
     * e a mesma instância é reutilizada para chamadas subsequentes.
     */
    val apiService: ApiService by lazy {
        Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(ApiService::class.java)
    }
}