/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity; :com.example.myapplication.domain.repository.AuthRepository0 /com.example.myapplication.domain.model.Resource0 /com.example.myapplication.domain.model.Resource0 /com.example.myapplication.domain.model.Resource/ .com.example.myapplication.ui.login.LoginResult/ .com.example.myapplication.ui.login.LoginResult. -com.example.myapplication.ui.login.LoginEvent. -com.example.myapplication.ui.login.LoginEvent. -com.example.myapplication.ui.login.LoginEvent. -com.example.myapplication.ui.login.LoginEvent. -com.example.myapplication.ui.login.LoginEvent androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository; :com.example.myapplication.domain.repository.AuthRepository5 4com.example.myapplication.ui.register.RegisterResult5 4com.example.myapplication.ui.register.RegisterResult4 3com.example.myapplication.ui.register.RegisterEvent4 3com.example.myapplication.ui.register.RegisterEvent4 3com.example.myapplication.ui.register.RegisterEvent4 3com.example.myapplication.ui.register.RegisterEvent4 3com.example.myapplication.ui.register.RegisterEvent4 3com.example.myapplication.ui.register.RegisterEvent4 3com.example.myapplication.ui.register.RegisterEvent4 3com.example.myapplication.ui.register.RegisterEvent androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory$ #androidx.activity.ComponentActivity; :com.example.myapplication.domain.repository.AuthRepository