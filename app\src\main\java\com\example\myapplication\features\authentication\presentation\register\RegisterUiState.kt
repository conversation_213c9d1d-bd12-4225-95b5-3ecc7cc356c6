package com.example.myapplication.features.authentication.presentation.register

import com.example.myapplication.features.authentication.domain.model.RegisterResponse

/**
 * Estado da UI da tela de registro
 * Seguindo o padrão MVVM e princípio da Responsabilidade Única (SRP)
 */
data class RegisterUiState(
    val username: String = "",
    val email: String = "",
    val password: String = "",
    val confirmPassword: String = "",
    val fullName: String = "",
    val isLoading: Boolean = false,
    val isPasswordVisible: Boolean = false,
    val isConfirmPasswordVisible: Boolean = false,
    val registerResult: RegisterResult? = null,
    val errorMessage: String? = null,
    
    // Validação de campos individuais
    val isUsernameError: Boolean = false,
    val isEmailError: Boolean = false,
    val isPasswordError: Boolean = false,
    val isConfirmPasswordError: Boolean = false,
    val isFullNameError: Boolean = false,
    
    // Mensagens de erro específicas
    val usernameErrorMessage: String? = null,
    val emailErrorMessage: String? = null,
    val passwordErrorMessage: String? = null,
    val confirmPasswordErrorMessage: String? = null,
    val fullNameErrorMessage: String? = null
)

/**
 * Resultado do registro
 */
sealed class RegisterResult {
    data class Success(val registerResponse: RegisterResponse) : RegisterResult()
    data class Error(val message: String) : RegisterResult()
}

/**
 * Eventos da tela de registro
 */
sealed class RegisterUiEvent {
    data class UsernameChanged(val value: String) : RegisterUiEvent()
    data class EmailChanged(val value: String) : RegisterUiEvent()
    data class PasswordChanged(val value: String) : RegisterUiEvent()
    data class ConfirmPasswordChanged(val value: String) : RegisterUiEvent()
    data class FullNameChanged(val value: String) : RegisterUiEvent()
    object TogglePasswordVisibility : RegisterUiEvent()
    object ToggleConfirmPasswordVisibility : RegisterUiEvent()
    object RegisterClicked : RegisterUiEvent()
    object NavigateToLogin : RegisterUiEvent()
    object ClearError : RegisterUiEvent()
}
