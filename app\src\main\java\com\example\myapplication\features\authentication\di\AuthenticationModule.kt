package com.example.myapplication.features.authentication.di

import com.example.myapplication.core.network.IApiErrorHandler
import com.example.myapplication.core.storage.IPreferencesManager
import com.example.myapplication.features.authentication.data.local.AuthLocalDataSource
import com.example.myapplication.features.authentication.data.local.IAuthLocalDataSource
import com.example.myapplication.features.authentication.data.remote.AuthRemoteDataSource
import com.example.myapplication.features.authentication.data.remote.IAuthRemoteDataSource
import com.example.myapplication.features.authentication.data.remote.api.IAuthApiService
import com.example.myapplication.features.authentication.data.repository.AuthRepositoryImpl
import com.example.myapplication.features.authentication.domain.repository.IAuthRepository
import com.example.myapplication.features.authentication.domain.usecase.CheckAuthenticationUseCase
import com.example.myapplication.features.authentication.domain.usecase.ICheckAuthenticationUseCase
import com.example.myapplication.features.authentication.domain.usecase.ILoginUseCase
import com.example.myapplication.features.authentication.domain.usecase.ILogoutUseCase
import com.example.myapplication.features.authentication.domain.usecase.IRefreshTokenUseCase
import com.example.myapplication.features.authentication.domain.usecase.IRegisterUseCase
import com.example.myapplication.features.authentication.domain.usecase.LoginUseCase
import com.example.myapplication.features.authentication.domain.usecase.LogoutUseCase
import com.example.myapplication.features.authentication.domain.usecase.RefreshTokenUseCase
import com.example.myapplication.features.authentication.domain.usecase.RegisterUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton

/**
 * Módulo de DI para o feature de autenticação
 * Seguindo o princípio da Responsabilidade Única (SRP)
 */
@Module
@InstallIn(SingletonComponent::class)
object AuthenticationModule {
    
    // API Service
    @Provides
    @Singleton
    fun provideAuthApiService(retrofit: Retrofit): IAuthApiService {
        return retrofit.create(IAuthApiService::class.java)
    }
    
    // Data Sources
    @Provides
    @Singleton
    fun provideAuthLocalDataSource(
        preferencesManager: IPreferencesManager
    ): IAuthLocalDataSource {
        return AuthLocalDataSource(preferencesManager)
    }
    
    @Provides
    @Singleton
    fun provideAuthRemoteDataSource(
        apiService: IAuthApiService,
        errorHandler: IApiErrorHandler
    ): IAuthRemoteDataSource {
        return AuthRemoteDataSource(apiService, errorHandler)
    }
    
    // Repository
    @Provides
    @Singleton
    fun provideAuthRepository(
        remoteDataSource: IAuthRemoteDataSource,
        localDataSource: IAuthLocalDataSource
    ): IAuthRepository {
        return AuthRepositoryImpl(remoteDataSource, localDataSource)
    }
    
    // Use Cases
    @Provides
    fun provideLoginUseCase(
        authRepository: IAuthRepository
    ): ILoginUseCase {
        return LoginUseCase(authRepository)
    }
    
    @Provides
    fun provideRegisterUseCase(
        authRepository: IAuthRepository
    ): IRegisterUseCase {
        return RegisterUseCase(authRepository)
    }
    
    @Provides
    fun provideLogoutUseCase(
        authRepository: IAuthRepository
    ): ILogoutUseCase {
        return LogoutUseCase(authRepository)
    }
    
    @Provides
    fun provideCheckAuthenticationUseCase(
        authRepository: IAuthRepository
    ): ICheckAuthenticationUseCase {
        return CheckAuthenticationUseCase(authRepository)
    }
    
    @Provides
    fun provideRefreshTokenUseCase(
        authRepository: IAuthRepository
    ): IRefreshTokenUseCase {
        return RefreshTokenUseCase(authRepository)
    }
}
