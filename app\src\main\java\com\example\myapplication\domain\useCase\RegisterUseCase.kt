package com.example.myapplication.domain.useCase

import com.example.myapplication.domain.model.RegisterRequest
import com.example.myapplication.domain.model.RegisterResponse
import com.example.myapplication.domain.model.Resource
import com.example.myapplication.domain.repository.AuthRepository

/**
 * Use case para registro de novos usuários
 * Responsável por validar os dados de entrada e chamar o repositório
 */
class RegisterUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(
        username: String,
        email: String,
        password: String,
        fullName: String? = null
    ): Resource<RegisterResponse> {
        // Validações de entrada
        when {
            username.isBlank() -> return Resource.Error("Nome de usuário é obrigatório")
            username.length < 3 -> return Resource.Error("Nome de usuário deve ter pelo menos 3 caracteres")
            username.length > 30 -> return Resource.Error("Nome de usuário deve ter no máximo 30 caracteres")
            !username.matches(Regex("^[a-zA-Z0-9_]+$")) -> return Resource.Error("Nome de usuário deve conter apenas letras, números e underscore")
            
            email.isBlank() -> return Resource.Error("Email é obrigatório")
            !isValidEmail(email) -> return Resource.Error("Email deve ser um email válido")
            
            password.isBlank() -> return Resource.Error("Senha é obrigatória")
            password.length < 6 -> return Resource.Error("Senha deve ter pelo menos 6 caracteres")
            password.length > 100 -> return Resource.Error("Senha deve ter no máximo 100 caracteres")
            
            fullName != null && fullName.isNotBlank() && fullName.length > 100 -> 
                return Resource.Error("Nome completo deve ter no máximo 100 caracteres")
        }

        val registerRequest = RegisterRequest(
            username = username.trim(),
            email = email.trim().lowercase(),
            password = password,
            fullName = fullName?.trim()?.takeIf { it.isNotBlank() }
        )
        
        return authRepository.register(registerRequest)
    }

    /**
     * Valida se o email tem um formato válido
     */
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
